/**
 * 简单的 Electron 启动脚本
 * 用于在没有完整安装 Electron 依赖的情况下测试基本功能
 */

const { spawn } = require('child_process')
const path = require('path')

console.log('🚀 启动 Live2D 桌面宠物应用...')

// 检查是否安装了 Electron
try {
  require('electron')
  console.log('✅ 检测到 Electron 已安装')
} catch (error) {
  console.log('❌ 未检测到 Electron，请先安装依赖:')
  console.log('   npm install electron --save-dev')
  console.log('   或者')
  console.log('   pnpm add -D electron')
  process.exit(1)
}

// 设置环境变量
process.env.NODE_ENV = 'development'

// 启动 Electron 应用
const electronPath = require('electron')
const mainPath = path.join(__dirname, 'electron', 'main.js')

console.log('📂 主进程文件:', mainPath)
console.log('🔧 启动模式: 开发模式')

const electronProcess = spawn(electronPath, [mainPath], {
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_ENV: 'development'
  }
})

electronProcess.on('close', (code) => {
  console.log(`\n🏁 Electron 进程退出，退出码: ${code}`)
  process.exit(code)
})

electronProcess.on('error', (error) => {
  console.error('❌ 启动 Electron 失败:', error)
  process.exit(1)
})

// 处理进程信号
process.on('SIGINT', () => {
  console.log('\n🛑 收到中断信号，正在关闭应用...')
  electronProcess.kill('SIGINT')
})

process.on('SIGTERM', () => {
  console.log('\n🛑 收到终止信号，正在关闭应用...')
  electronProcess.kill('SIGTERM')
})
