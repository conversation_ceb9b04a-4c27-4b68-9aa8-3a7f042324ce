<!--
  桌面宠物控制组件
  
  功能说明:
  - 提供隐藏式的控制界面
  - 支持悬浮图标、右键菜单等交互方式
  - 集成所有现有的Live2D功能
  - 适配桌面宠物的使用场景
-->

<template>
  <div class="desktop-pet-controls">
    <!-- 悬浮控制按钮 -->
    <div
      class="floating-controls"
      :class="{ 'visible': showControls }"
      @mouseenter="showControls = true"
      @mouseleave="handleMouseLeave"
    >
      <!-- 主控制按钮 -->
      <div class="main-control-btn" @click="toggleMainPanel">
        <span class="icon">⚙️</span>
        <span class="tooltip">设置</span>
      </div>
      
      <!-- 快速控制按钮组 -->
      <div class="quick-controls" v-show="showControls">
        <button
          class="control-btn"
          @click="toggleExpressionsPanel"
          title="表情控制"
        >
          🎭
        </button>
        <button
          class="control-btn"
          @click="toggleMotionsPanel"
          title="动作控制"
        >
          🎬
        </button>
        <button
          class="control-btn"
          @click="toggleAudioPanel"
          title="音频控制"
        >
          🔊
        </button>
        <button
          class="control-btn"
          @click="toggleLipSyncPanel"
          title="口型同步"
        >
          🗣️
        </button>
        <button
          class="control-btn"
          @click="toggleAlwaysOnTop"
          :class="{ 'active': isAlwaysOnTop }"
          title="始终置顶"
        >
          📌
        </button>
        <button
          class="control-btn"
          @click="minimizeToTray"
          title="最小化到托盘"
        >
          ➖
        </button>
      </div>
    </div>

    <!-- 主设置面板 -->
    <div
      class="main-panel"
      v-show="showMainPanel"
      :style="panelStyle"
    >
      <div class="panel-header">
        <h3>🐾 桌面宠物设置</h3>
        <button class="close-btn" @click="showMainPanel = false">❌</button>
      </div>
      
      <div class="panel-content">
        <!-- 模型选择 -->
        <div class="setting-group">
          <label>模型选择:</label>
          <select v-model="selectedModel" @change="handleModelChange">
            <option value="idol">偶像</option>
            <option value="lanhei">蓝黑</option>
            <option value="hibiki">Hibiki</option>
            <option value="hiyori">Hiyori</option>
            <option value="mark">Mark</option>
            <option value="natori">Natori</option>
            <option value="kei_basic">Kei Basic</option>
            <option value="kei_vowels">Kei Vowels Pro</option>
          </select>
        </div>

        <!-- 模型缩放 -->
        <div class="setting-group">
          <label>模型大小: {{ modelScale }}%</label>
          <input
            type="range"
            min="50"
            max="200"
            step="10"
            v-model="modelScale"
            @input="$emit('scale-change', modelScale / 100)"
          >
        </div>

        <!-- 透明度控制 -->
        <div class="setting-group">
          <label>透明度: {{ opacity }}%</label>
          <input
            type="range"
            min="20"
            max="100"
            step="5"
            v-model="opacity"
            @input="updateOpacity"
          >
        </div>

        <!-- 位置锁定 -->
        <div class="setting-group">
          <label>
            <input
              type="checkbox"
              v-model="isPositionLocked"
              @change="togglePositionLock"
            >
            锁定位置
          </label>
        </div>
      </div>
    </div>

    <!-- 表情控制面板 -->
    <div
      class="expressions-panel panel"
      v-show="showExpressionsPanel"
      :style="panelStyle"
    >
      <div class="panel-header">
        <h3>🎭 表情控制</h3>
        <button class="close-btn" @click="showExpressionsPanel = false">❌</button>
      </div>
      <div class="panel-content">
        <slot name="expressions-content"></slot>
      </div>
    </div>

    <!-- 动作控制面板 -->
    <div
      class="motions-panel panel"
      v-show="showMotionsPanel"
      :style="panelStyle"
    >
      <div class="panel-header">
        <h3>🎬 动作控制</h3>
        <button class="close-btn" @click="showMotionsPanel = false">❌</button>
      </div>
      <div class="panel-content">
        <slot name="motions-content"></slot>
      </div>
    </div>

    <!-- 音频控制面板 -->
    <div
      class="audio-panel panel"
      v-show="showAudioPanel"
      :style="panelStyle"
    >
      <div class="panel-header">
        <h3>🔊 音频控制</h3>
        <button class="close-btn" @click="showAudioPanel = false">❌</button>
      </div>
      <div class="panel-content">
        <slot name="audio-content"></slot>
      </div>
    </div>

    <!-- 口型同步面板 -->
    <div
      class="lipsync-panel panel"
      v-show="showLipSyncPanel"
      :style="panelStyle"
    >
      <div class="panel-header">
        <h3>🗣️ 口型同步</h3>
        <button class="close-btn" @click="showLipSyncPanel = false">❌</button>
      </div>
      <div class="panel-content">
        <slot name="lipsync-content"></slot>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div
      class="context-menu"
      v-show="showContextMenu"
      :style="contextMenuStyle"
      @click.stop
    >
      <div class="menu-item" @click="toggleExpressionsPanel">🎭 表情控制</div>
      <div class="menu-item" @click="toggleMotionsPanel">🎬 动作控制</div>
      <div class="menu-item" @click="toggleAudioPanel">🔊 音频控制</div>
      <div class="menu-item" @click="toggleLipSyncPanel">🗣️ 口型同步</div>
      <div class="menu-separator"></div>
      <div class="menu-item" @click="toggleAlwaysOnTop">
        📌 始终置顶 {{ isAlwaysOnTop ? '✓' : '' }}
      </div>
      <div class="menu-item" @click="togglePositionLock">
        🔒 锁定位置 {{ isPositionLocked ? '✓' : '' }}
      </div>
      <div class="menu-separator"></div>
      <div class="menu-item" @click="resetPosition">🏠 重置位置</div>
      <div class="menu-item" @click="minimizeToTray">➖ 最小化</div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

// === Props 和 Emits ===
const props = defineProps({
  selectedModel: {
    type: String,
    default: 'idol'
  },
  isModelLoaded: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'model-change',
  'scale-change',
  'opacity-change',
  'position-lock-change',
  'always-on-top-change'
])

// === 响应式数据 ===
const showControls = ref(false)
const showMainPanel = ref(false)
const showExpressionsPanel = ref(false)
const showMotionsPanel = ref(false)
const showAudioPanel = ref(false)
const showLipSyncPanel = ref(false)
const showContextMenu = ref(false)

const selectedModel = ref(props.selectedModel)

// 监听props变化，同步更新本地状态
watch(() => props.selectedModel, (newValue) => {
  selectedModel.value = newValue
})
const modelScale = ref(100)
const opacity = ref(100)
const isAlwaysOnTop = ref(true)
const isPositionLocked = ref(false)

const contextMenuPosition = ref({ x: 0, y: 0 })
const controlsHideTimer = ref(null)

// === 计算属性 ===
const panelStyle = computed(() => ({
  position: 'fixed',
  top: '50px',
  right: '10px',
  zIndex: 1000
}))

const contextMenuStyle = computed(() => ({
  position: 'fixed',
  left: `${contextMenuPosition.value.x}px`,
  top: `${contextMenuPosition.value.y}px`,
  zIndex: 1001
}))

// === 方法 ===

/**
 * 切换主面板显示
 */
function toggleMainPanel() {
  showMainPanel.value = !showMainPanel.value
  hideOtherPanels('main')
}

/**
 * 切换表情面板
 */
function toggleExpressionsPanel() {
  showExpressionsPanel.value = !showExpressionsPanel.value
  hideOtherPanels('expressions')
  hideContextMenu()
}

/**
 * 切换动作面板
 */
function toggleMotionsPanel() {
  showMotionsPanel.value = !showMotionsPanel.value
  hideOtherPanels('motions')
  hideContextMenu()
}

/**
 * 切换音频面板
 */
function toggleAudioPanel() {
  showAudioPanel.value = !showAudioPanel.value
  hideOtherPanels('audio')
  hideContextMenu()
}

/**
 * 切换口型同步面板
 */
function toggleLipSyncPanel() {
  showLipSyncPanel.value = !showLipSyncPanel.value
  hideOtherPanels('lipsync')
  hideContextMenu()
}

/**
 * 隐藏其他面板
 */
function hideOtherPanels(except) {
  if (except !== 'main') showMainPanel.value = false
  if (except !== 'expressions') showExpressionsPanel.value = false
  if (except !== 'motions') showMotionsPanel.value = false
  if (except !== 'audio') showAudioPanel.value = false
  if (except !== 'lipsync') showLipSyncPanel.value = false
}

/**
 * 处理鼠标离开控制区域
 */
function handleMouseLeave() {
  controlsHideTimer.value = setTimeout(() => {
    showControls.value = false
  }, 1000) // 1秒后隐藏
}

/**
 * 处理模型切换
 */
function handleModelChange() {
  console.log('桌面宠物控制：模型切换到', selectedModel.value)
  emit('model-change', selectedModel.value)
}

/**
 * 更新透明度
 */
function updateOpacity() {
  document.body.style.opacity = opacity.value / 100
  emit('opacity-change', opacity.value / 100)
}

/**
 * 切换始终置顶
 */
async function toggleAlwaysOnTop() {
  if (window.desktopPet) {
    try {
      const newState = await window.desktopPet.toggleAlwaysOnTop()
      isAlwaysOnTop.value = newState
      emit('always-on-top-change', newState)
    } catch (error) {
      console.error('切换置顶状态失败:', error)
    }
  }
  hideContextMenu()
}

/**
 * 切换位置锁定
 */
function togglePositionLock() {
  isPositionLocked.value = !isPositionLocked.value
  
  if (window.desktopPet) {
    if (isPositionLocked.value) {
      window.desktopPet.disableWindowDrag()
    } else {
      window.desktopPet.enableWindowDrag()
    }
  }
  
  emit('position-lock-change', isPositionLocked.value)
  hideContextMenu()
}

/**
 * 重置位置
 */
function resetPosition() {
  // 这里可以调用 Electron 主进程的重置位置功能
  console.log('重置位置到默认位置')
  hideContextMenu()
}

/**
 * 最小化到托盘
 */
function minimizeToTray() {
  if (window.desktopPet) {
    // 隐藏窗口（Electron 会自动处理托盘显示）
    window.close()
  }
  hideContextMenu()
}

/**
 * 显示右键菜单
 */
function showContextMenuAt(x, y) {
  contextMenuPosition.value = { x, y }
  showContextMenu.value = true
}

/**
 * 隐藏右键菜单
 */
function hideContextMenu() {
  showContextMenu.value = false
}

/**
 * 处理右键点击
 */
function handleContextMenu(event) {
  event.preventDefault()
  showContextMenuAt(event.clientX, event.clientY)
}

/**
 * 处理点击其他区域隐藏菜单
 */
function handleClickOutside() {
  hideContextMenu()
}

// === 生命周期 ===
onMounted(() => {
  // 启用窗口拖拽
  if (window.desktopPet) {
    window.desktopPet.enableWindowDrag()
    
    // 监听位置锁定状态变化
    window.desktopPet.onPositionLockChanged((locked) => {
      isPositionLocked.value = locked
    })
  }
  
  // 添加右键菜单监听
  document.addEventListener('contextmenu', handleContextMenu)
  document.addEventListener('click', handleClickOutside)
  
  // 设置初始透明度
  updateOpacity()
})

onUnmounted(() => {
  // 清理事件监听
  document.removeEventListener('contextmenu', handleContextMenu)
  document.removeEventListener('click', handleClickOutside)
  
  if (controlsHideTimer.value) {
    clearTimeout(controlsHideTimer.value)
  }
})

// 暴露方法给父组件
defineExpose({
  showContextMenuAt,
  hideContextMenu,
  toggleMainPanel
})
</script>

<style scoped>
/* === 桌面宠物控制样式 === */

.desktop-pet-controls {
  position: relative;
  pointer-events: none; /* 默认不阻止鼠标事件 */
}

/* === 悬浮控制按钮 === */
.floating-controls {
  position: fixed;
  bottom: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  pointer-events: auto;
  z-index: 999;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.floating-controls:hover {
  opacity: 1;
}

.floating-controls.visible .quick-controls {
  animation: slideIn 0.3s ease;
}

.main-control-btn {
  position: relative;
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.main-control-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

.main-control-btn .icon {
  font-size: 18px;
}

.main-control-btn .tooltip {
  position: absolute;
  right: 50px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.main-control-btn:hover .tooltip {
  opacity: 1;
}

.quick-controls {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.control-btn {
  width: 32px;
  height: 32px;
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

.control-btn.active {
  background: rgba(52, 152, 219, 0.8);
  border-color: rgba(52, 152, 219, 1);
}

/* === 面板通用样式 === */
.panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 280px;
  max-width: 400px;
  max-height: 500px;
  overflow: hidden;
  pointer-events: auto;
  animation: panelSlideIn 0.3s ease;
}

.main-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 300px;
  max-width: 400px;
  max-height: 600px;
  overflow: hidden;
  pointer-events: auto;
  animation: panelSlideIn 0.3s ease;
}

.panel-header {
  background: rgba(0, 0, 0, 0.1);
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 0, 0, 0.1);
}

.panel-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

/* === 设置组样式 === */
.setting-group {
  margin-bottom: 16px;
}

.setting-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.setting-group select,
.setting-group input[type="range"] {
  width: 100%;
  padding: 6px;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.8);
}

.setting-group input[type="checkbox"] {
  margin-right: 8px;
}

/* === 右键菜单样式 === */
.context-menu {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 180px;
  overflow: hidden;
  pointer-events: auto;
  animation: contextMenuSlideIn 0.2s ease;
}

.menu-item {
  padding: 10px 16px;
  cursor: pointer;
  transition: background 0.2s ease;
  font-size: 14px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.menu-item:hover {
  background: rgba(0, 0, 0, 0.1);
}

.menu-separator {
  height: 1px;
  background: rgba(0, 0, 0, 0.1);
  margin: 4px 0;
}

/* === 动画效果 === */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes panelSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes contextMenuSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* === 响应式设计 === */
@media (max-width: 480px) {
  .panel,
  .main-panel {
    min-width: 250px;
    max-width: 90vw;
  }

  .floating-controls {
    bottom: 20px;
    right: 20px;
  }
}

/* === 滚动条样式 === */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}
</style>
